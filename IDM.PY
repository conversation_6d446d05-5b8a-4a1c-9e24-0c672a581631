
import os
import threading
import requests
from pytube import YouTube
from tqdm import tqdm
import tkinter as tk
from tkinter import filedialog, messagebox
import pyperclip

# ------------------- تحميل من رابط مباشر -------------------
def download_file(url, output_path=None):
    try:
        local_filename = url.split("/")[-1]
        if output_path:
            os.makedirs(output_path, exist_ok=True)
            local_filename = os.path.join(output_path, local_filename)

        resume_header = {}
        file_size = 0
        if os.path.exists(local_filename):
            file_size = os.path.getsize(local_filename)
            resume_header = {'Range': f'bytes={file_size}-'}

        with requests.get(url, stream=True, headers=resume_header) as r:
            total_size = int(r.headers.get('content-length', 0)) + file_size
            if r.status_code not in [200, 206]:
                raise Exception(f"فشل التحميل. الكود: {r.status_code}")

            progress = tqdm(total=total_size, initial=file_size, unit='B', unit_scale=True, desc=local_filename)
            mode = 'ab' if file_size else 'wb'

            with open(local_filename, mode) as f:
                for chunk in r.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        progress.update(len(chunk))
            progress.close()
        messagebox.showinfo("نجاح", f"تم تحميل الملف بنجاح: {local_filename}")
    except Exception as e:
        messagebox.showerror("خطأ", str(e))

# ------------------- تحميل فيديو YouTube -------------------
def download_youtube(url, output_path=None, audio_only=False):
    try:
        yt = YouTube(url)
        os.makedirs(output_path, exist_ok=True)
        if audio_only:
            stream = yt.streams.filter(only_audio=True).order_by('abr').desc().first()
            out_file = stream.download(output_path=output_path)
            base, ext = os.path.splitext(out_file)
            new_file = base + '.mp3'
            os.rename(out_file, new_file)
            messagebox.showinfo("نجاح", f"✅ تم تحميل الصوت فقط: {yt.title}")
        else:
            stream = yt.streams.filter(progressive=True, file_extension='mp4').order_by('resolution').desc().first()
            stream.download(output_path=output_path)
            messagebox.showinfo("نجاح", f"✅ تم تحميل الفيديو: {yt.title}")
    except Exception as e:
        messagebox.showerror("خطأ YouTube", str(e))

# ------------------- إجراءات عند الضغط على زر التحميل -------------------
def start_download():
    url = url_entry.get()
    folder = folder_entry.get()
    if not url:
        messagebox.showwarning("تنبيه", "يرجى إدخال الرابط أولاً")
        return

    if "youtube.com" in url or "youtu.be" in url:
        audio_only = audio_var.get()
        threading.Thread(target=download_youtube, args=(url, folder, audio_only)).start()
    else:
        threading.Thread(target=download_file, args=(url, folder)).start()

def browse_folder():
    folder = filedialog.askdirectory()
    if folder:
        folder_entry.delete(0, tk.END)
        folder_entry.insert(0, folder)

# ------------------- واجهة المستخدم -------------------
root = tk.Tk()
root.title("مدير التحميل الذكي")
root.geometry("550x250")

tk.Label(root, text="📥 أدخل الرابط (أو سيتم لصقه تلقائيًا):").pack(pady=5)

url_entry = tk.Entry(root, width=80)
url_entry.pack(pady=5)

# لصق الرابط من الحافظة تلقائيًا
try:
    clipboard_url = pyperclip.paste()
    if clipboard_url.startswith("http"):
        url_entry.insert(0, clipboard_url)
except:
    pass  # لو pyperclip لم تعمل

tk.Label(root, text="📂 مجلد الحفظ:").pack(pady=5)
folder_frame = tk.Frame(root)
folder_frame.pack()
folder_entry = tk.Entry(folder_frame, width=50)
folder_entry.pack(side=tk.LEFT)
tk.Button(folder_frame, text="استعراض", command=browse_folder).pack(side=tk.LEFT, padx=5)

# خيار تحميل الصوت فقط
audio_var = tk.BooleanVar()
tk.Checkbutton(root, text="🎵 تحميل الصوت فقط (YouTube)", variable=audio_var).pack(pady=5)

tk.Button(root, text="⬇️ ابدأ التحميل", command=start_download, bg="green", fg="white").pack(pady=10)

root.mainloop()
