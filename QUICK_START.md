# 🚀 دليل البدء السريع - Quick Start Guide

## ⚡ التشغيل السريع

### Windows
```bash
# انقر مرتين على الملف
run.bat
```

### Linux/Mac
```bash
# في الطرفية
./run.sh
```

### يدوياً
```bash
# تثبيت المتطلبات
pip install -r requirements.txt

# تشغيل التطبيق
python smart_downloader_gui.py
```

## 📋 خطوات الاستخدام

### 1️⃣ إدخال الرابط
- الصق الرابط في الحقل الأول
- أو استخدم زر "📋 لصق" لللصق من الحافظة
- الرابط سيُلصق تلقائياً إذا كان في الحافظة

### 2️⃣ اختيار مجلد الحفظ (اختياري)
- اضغط "📁 استعراض" لاختيار مجلد
- أو اتركه فارغاً للحفظ في مجلد التطبيق

### 3️⃣ خيارات YouTube (اختياري)
- ✅ فعّل "🎵 تحميل الصوت فقط" لتحميل MP3
- ❌ اتركه معطلاً لتحميل الفيديو كاملاً

### 4️⃣ بدء التحميل
- اضغط "⬇️ ابدأ التحميل"
- راقب شريط التقدم ورسائل الحالة
- انتظر رسالة النجاح

## 🔗 أنواع الروابط المدعومة

### ✅ مدعومة
- `https://youtube.com/watch?v=...`
- `https://youtu.be/...`
- `https://example.com/file.zip`
- `http://site.com/document.pdf`
- أي رابط مباشر لملف

### ❌ غير مدعومة
- روابط تتطلب تسجيل دخول
- روابط محمية بكلمة مرور
- روابط منتهية الصلاحية
- روابط غير صحيحة

## 🎯 نصائح للاستخدام الأمثل

### للملفات الكبيرة
- تأكد من وجود مساحة كافية
- لا تغلق التطبيق أثناء التحميل
- يمكن استكمال التحميل إذا انقطع

### لفيديوهات YouTube
- استخدم خيار "الصوت فقط" لتوفير المساحة
- بعض الفيديوهات قد تحتاج وقت أطول للتحليل
- تأكد من أن الفيديو متاح في منطقتك

### لتحسين الأداء
- أغلق البرامج غير الضرورية
- استخدم اتصال إنترنت مستقر
- تجنب تحميل ملفات متعددة في نفس الوقت

## 🔧 حل المشاكل الشائعة

### "خطأ في الرابط"
- تأكد من صحة الرابط
- تأكد من بدء الرابط بـ http:// أو https://
- جرب نسخ الرابط مرة أخرى

### "فشل التحميل"
- تحقق من اتصال الإنترنت
- تأكد من وجود مساحة كافية
- جرب رابط آخر للتأكد

### "خطأ YouTube"
- تأكد من أن الفيديو متاح للعامة
- جرب رابط فيديو آخر
- تحديث pytube: `pip install --upgrade pytube`

### "لا يمكن الوصول للحافظة"
- أعد تشغيل التطبيق
- انسخ الرابط يدوياً
- تأكد من صلاحيات التطبيق

## 📞 الحصول على المساعدة

### الموارد المتاحة
- `README.md` - دليل شامل
- `FEATURES.md` - قائمة الميزات
- `config.py` - إعدادات التطبيق

### الإبلاغ عن المشاكل
1. وصف المشكلة بالتفصيل
2. ذكر نوع الرابط المستخدم
3. إرفاق رسالة الخطأ إن وجدت
4. ذكر نظام التشغيل المستخدم

---
**💡 نصيحة**: احتفظ بهذا الملف مرجعاً سريعاً للاستخدام!
