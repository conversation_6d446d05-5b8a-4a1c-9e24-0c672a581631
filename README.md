# 📥 مدير التحميل الذكي - Smart Downloader

واجهة رسومية احترافية لتحميل الملفات وفيديوهات YouTube بسهولة وأناقة.

## ✨ المميزات

- **تحميل الملفات العادية**: يدعم تحميل أي ملف من الإنترنت مع إمكانية استكمال التحميل
- **تحميل YouTube**: تحميل الفيديوهات والصوتيات من YouTube
- **واجهة احترافية**: تصميم عصري وأنيق باللغة العربية
- **شريط التقدم**: متابعة حالة التحميل بصريًا
- **لصق تلقائي**: لصق الروابط تلقائيًا من الحافظة
- **اختيار المجلد**: إمكانية اختيار مجلد الحفظ
- **خيارات متقدمة**: تحميل الصوت فقط من YouTube

## 🚀 كيفية الاستخدام

### التثبيت
```bash
pip install -r requirements.txt
```

### تشغيل التطبيق
```bash
python smart_downloader_gui.py
```

### الاستخدام
1. **إدخال الرابط**: أدخل رابط الملف أو فيديو YouTube
2. **اختيار المجلد**: اختر مجلد الحفظ (اختياري)
3. **خيارات YouTube**: للفيديوهات، يمكنك اختيار تحميل الصوت فقط
4. **بدء التحميل**: اضغط على زر "ابدأ التحميل"

## 📋 المتطلبات

- Python 3.7+
- requests
- pytube
- pyperclip
- tkinter (مدمج مع Python)

## 🎯 الميزات التقنية

- **استكمال التحميل**: يدعم استكمال التحميل المتقطع
- **معالجة الأخطاء**: معالجة شاملة للأخطاء مع رسائل واضحة
- **واجهة متجاوبة**: لا تتجمد أثناء التحميل
- **أمان**: التحقق من صحة الروابط قبل التحميل

## 🔧 التخصيص

يمكنك تخصيص التطبيق عبر تعديل:
- الألوان والخطوط في الكود
- حجم النافذة
- رسائل الحالة

## 📝 ملاحظات

- يتطلب اتصال بالإنترنت
- بعض فيديوهات YouTube قد تتطلب معالجة خاصة
- يُنصح بتحديث pytube بانتظام للحصول على أفضل دعم لـ YouTube

## 🤝 المساهمة

نرحب بالمساهمات! يمكنك:
- الإبلاغ عن الأخطاء
- اقتراح ميزات جديدة
- تحسين الكود
- ترجمة التطبيق

---
**تم تطويره بـ ❤️ لتسهيل عملية التحميل**
