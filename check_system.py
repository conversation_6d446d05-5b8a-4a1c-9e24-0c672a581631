#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص النظام - System Check
التحقق من توفر جميع المتطلبات لتشغيل مدير التحميل الذكي
"""

import sys
import subprocess
import importlib
import platform

def check_python_version():
    """التحقق من إصدار Python"""
    print("🐍 فحص إصدار Python...")
    version = sys.version_info
    print(f"   الإصدار الحالي: {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 7:
        print("   ✅ إصدار Python مناسب")
        return True
    else:
        print("   ❌ يتطلب Python 3.7 أو أحدث")
        return False

def check_package(package_name, import_name=None):
    """التحقق من توفر حزمة Python"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        print(f"   ✅ {package_name} متوفر")
        return True
    except ImportError:
        print(f"   ❌ {package_name} غير متوفر")
        return False

def check_packages():
    """التحقق من جميع الحزم المطلوبة"""
    print("\n📦 فحص الحزم المطلوبة...")
    
    packages = [
        ("tkinter", "tkinter"),
        ("requests", "requests"),
        ("pytube", "pytube"),
        ("pyperclip", "pyperclip")
    ]
    
    all_available = True
    for package_name, import_name in packages:
        if not check_package(package_name, import_name):
            all_available = False
    
    return all_available

def check_system_info():
    """عرض معلومات النظام"""
    print("\n💻 معلومات النظام:")
    print(f"   نظام التشغيل: {platform.system()} {platform.release()}")
    print(f"   المعمارية: {platform.machine()}")
    print(f"   Python: {platform.python_version()}")

def install_missing_packages():
    """تثبيت الحزم المفقودة"""
    print("\n🔧 تثبيت الحزم المفقودة...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("   ✅ تم تثبيت جميع الحزم بنجاح")
        return True
    except subprocess.CalledProcessError:
        print("   ❌ فشل في تثبيت الحزم")
        return False
    except FileNotFoundError:
        print("   ❌ ملف requirements.txt غير موجود")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🔍 فحص النظام - مدير التحميل الذكي")
    print("=" * 50)
    
    # فحص إصدار Python
    python_ok = check_python_version()
    
    # عرض معلومات النظام
    check_system_info()
    
    # فحص الحزم
    packages_ok = check_packages()
    
    # النتيجة النهائية
    print("\n" + "=" * 50)
    if python_ok and packages_ok:
        print("🎉 النظام جاهز! يمكنك تشغيل التطبيق الآن")
        print("   تشغيل: python smart_downloader_gui.py")
    elif python_ok and not packages_ok:
        print("⚠️  Python جاهز لكن بعض الحزم مفقودة")
        install = input("هل تريد تثبيت الحزم المفقودة؟ (y/n): ")
        if install.lower() in ['y', 'yes', 'نعم']:
            if install_missing_packages():
                print("🎉 تم إعداد النظام بنجاح!")
            else:
                print("❌ فشل في إعداد النظام")
    else:
        print("❌ النظام غير جاهز - يرجى تحديث Python")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
