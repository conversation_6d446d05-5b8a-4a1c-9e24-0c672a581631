@echo off
echo ========================================
echo      مدير التحميل الذكي
echo      Smart Downloader
echo ========================================
echo.

echo جاري التحقق من المتطلبات...
echo Checking requirements...

python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo Error: Python is not installed
    pause
    exit /b 1
)

echo جاري تثبيت المتطلبات...
echo Installing requirements...
pip install -r requirements.txt

if errorlevel 1 (
    echo خطأ في تثبيت المتطلبات
    echo Error installing requirements
    pause
    exit /b 1
)

echo.
echo جاري تشغيل التطبيق...
echo Starting application...
echo.

python smart_downloader_gui.py

if errorlevel 1 (
    echo خطأ في تشغيل التطبيق
    echo Error running application
    pause
)
