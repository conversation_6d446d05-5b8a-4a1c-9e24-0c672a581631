#!/bin/bash

echo "========================================"
echo "      مدير التحميل الذكي"
echo "      Smart Downloader"
echo "========================================"
echo

echo "جاري التحقق من المتطلبات..."
echo "Checking requirements..."

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "خطأ: Python غير مثبت على النظام"
        echo "Error: Python is not installed"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "جاري تثبيت المتطلبات..."
echo "Installing requirements..."

# Check if pip is available
if command -v pip3 &> /dev/null; then
    pip3 install -r requirements.txt
elif command -v pip &> /dev/null; then
    pip install -r requirements.txt
else
    echo "خطأ: pip غير متوفر"
    echo "Error: pip is not available"
    exit 1
fi

if [ $? -ne 0 ]; then
    echo "خطأ في تثبيت المتطلبات"
    echo "Error installing requirements"
    exit 1
fi

echo
echo "جاري تشغيل التطبيق..."
echo "Starting application..."
echo

$PYTHON_CMD smart_downloader_gui.py

if [ $? -ne 0 ]; then
    echo "خطأ في تشغيل التطبيق"
    echo "Error running application"
    read -p "اضغط Enter للمتابعة..."
fi
