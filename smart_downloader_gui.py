import os
import threading
import requests
from pytube import YouTube
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import pyperclip
from tkinter import font
try:
    from config import COLORS, WINDOW, FONTS, MESSAGES, DOWNLOAD
except ImportError:
    # إعدادات افتراضية في حالة عدم وجود ملف config.py
    COLORS = {
        'primary': '#2c3e50', 'secondary': '#3498db', 'success': '#27ae60',
        'danger': '#e74c3c', 'info': '#2196F3', 'light': '#f5f5f5',
        'white': '#ffffff', 'dark': '#333333', 'purple': '#9b59b6',
        'green': '#4CAF50', 'gray': '#7f8c8d'
    }
    WINDOW = {'title': '📥 مدير التحميل الذكي', 'width': 700, 'height': 500}
    FONTS = {'title': ('Arial', 16, 'bold'), 'label': ('Arial', 10), 'button': ('Arial', 10, 'bold')}
    MESSAGES = {
        'ready': '📥 مدير التحميل الذكي - جاهز', 'downloading': '🔄 جاري التحميل...',
        'success_file': '✅ تم التحميل بنجاح!', 'error_download': '❌ فشل التحميل'
    }
    DOWNLOAD = {'chunk_size': 8192}


def download_file(url, output_path=None):
    try:
        # Update status
        status_label.config(text=MESSAGES['downloading'], fg=COLORS['info'])
        progress_bar['value'] = 0
        root.update()

        local_filename = url.split("/")[-1]
        if not local_filename or local_filename == url:
            local_filename = "downloaded_file"

        if output_path:
            os.makedirs(output_path, exist_ok=True)
            local_filename = os.path.join(output_path, local_filename)

        resume_header = {}
        file_size = 0
        if os.path.exists(local_filename):
            file_size = os.path.getsize(local_filename)
            resume_header = {'Range': f'bytes={file_size}-'}

        with requests.get(url, stream=True, headers=resume_header) as r:
            if r.status_code not in [200, 206]:
                raise Exception(f"فشل التحميل. الكود: {r.status_code}")

            total_size = int(r.headers.get('content-length', 0)) + file_size
            mode = 'ab' if file_size else 'wb'
            downloaded = file_size

            with open(local_filename, mode) as f:
                for chunk in r.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        if total_size > 0:
                            progress = (downloaded / total_size) * 100
                            progress_bar['value'] = progress
                            status_label.config(text=f"🔄 جاري التحميل... {progress:.1f}%")
                            root.update()

        status_label.config(text=MESSAGES['success_file'], fg=COLORS['green'])
        progress_bar['value'] = 100
        messagebox.showinfo("نجاح", f"تم تحميل الملف بنجاح:\n{local_filename}")
    except Exception as e:
        status_label.config(text=MESSAGES['error_download'], fg=COLORS['danger'])
        progress_bar['value'] = 0
        messagebox.showerror("خطأ", f"حدث خطأ أثناء التحميل:\n{str(e)}")


def download_youtube(url, output_path=None, audio_only=False):
    try:
        status_label.config(text=MESSAGES['analyzing'], fg=COLORS['info'])
        progress_bar['value'] = 0
        root.update()

        yt = YouTube(url)
        if output_path:
            os.makedirs(output_path, exist_ok=True)

        status_label.config(text=MESSAGES['downloading'], fg=COLORS['info'])
        progress_bar['value'] = 25
        root.update()

        if audio_only:
            stream = yt.streams.filter(only_audio=True).order_by('abr').desc().first()
            if not stream:
                raise Exception("لم يتم العثور على ملف صوتي متاح للتحميل")

            progress_bar['value'] = 50
            root.update()

            out_file = stream.download(output_path=output_path)
            base, _ = os.path.splitext(out_file)
            new_file = base + '.mp3'
            os.rename(out_file, new_file)

            progress_bar['value'] = 100
            status_label.config(text=MESSAGES['success_audio'], fg=COLORS['green'])
            messagebox.showinfo("نجاح", f"✅ تم تحميل الصوت فقط:\n{yt.title}")
        else:
            stream = yt.streams.filter(progressive=True, file_extension='mp4').order_by('resolution').desc().first()
            if not stream:
                stream = yt.streams.filter(file_extension='mp4').order_by('resolution').desc().first()
            if not stream:
                raise Exception("لم يتم العثور على ملف فيديو متاح للتحميل")

            progress_bar['value'] = 50
            root.update()

            stream.download(output_path=output_path)

            progress_bar['value'] = 100
            status_label.config(text=MESSAGES['success_video'], fg=COLORS['green'])
            messagebox.showinfo("نجاح", f"✅ تم تحميل الفيديو:\n{yt.title}")
    except Exception as e:
        status_label.config(text="❌ فشل تحميل YouTube", fg=COLORS['danger'])
        progress_bar['value'] = 0
        messagebox.showerror("خطأ YouTube", f"حدث خطأ أثناء تحميل الفيديو:\n{str(e)}")


def start_download():
    url = url_entry.get().strip()
    folder = folder_entry.get().strip()

    if not url:
        messagebox.showwarning("تنبيه", "يرجى إدخال الرابط أولاً")
        return

    if not url.startswith(('http://', 'https://')):
        messagebox.showwarning("تنبيه", "يرجى إدخال رابط صحيح يبدأ بـ http:// أو https://")
        return

    # Disable download button during download
    download_btn.config(state='disabled')
    status_label.config(text=MESSAGES['starting'], fg=COLORS['info'])

    def enable_button():
        download_btn.config(state='normal')

    if "youtube.com" in url or "youtu.be" in url:
        audio_only = audio_var.get()
        thread = threading.Thread(target=lambda: [download_youtube(url, folder, audio_only), enable_button()])
        thread.daemon = True
        thread.start()
    else:
        thread = threading.Thread(target=lambda: [download_file(url, folder), enable_button()])
        thread.daemon = True
        thread.start()


def browse_folder():
    folder = filedialog.askdirectory()
    if folder:
        folder_entry.delete(0, tk.END)
        folder_entry.insert(0, folder)


def paste_from_clipboard():
    try:
        clipboard_content = pyperclip.paste()
        if clipboard_content and clipboard_content.startswith(('http://', 'https://')):
            url_entry.delete(0, tk.END)
            url_entry.insert(0, clipboard_content)
            status_label.config(text=MESSAGES['clipboard_pasted'], fg=COLORS['green'])
    except Exception:
        messagebox.showwarning("تنبيه", "لا يمكن الوصول إلى الحافظة")


def clear_url():
    url_entry.delete(0, tk.END)
    status_label.config(text=MESSAGES['ready'], fg=COLORS['dark'])


# Create main window
root = tk.Tk()
root.title(WINDOW['title'])
root.geometry(f"{WINDOW['width']}x{WINDOW['height']}")
root.configure(bg=COLORS['light'])
root.resizable(True, False)

# Configure styles
title_font = font.Font(family=FONTS['title'][0], size=FONTS['title'][1], weight=FONTS['title'][2])
label_font = font.Font(family=FONTS['label'][0], size=FONTS['label'][1])
button_font = font.Font(family=FONTS['button'][0], size=FONTS['button'][1], weight=FONTS['button'][2])

# Main container
main_frame = tk.Frame(root, bg=COLORS['light'], padx=20, pady=20)
main_frame.pack(fill=tk.BOTH, expand=True)

# Title
title_label = tk.Label(main_frame, text="📥 مدير التحميل الذكي",
                      font=title_font, bg=COLORS['light'], fg=COLORS['primary'])
title_label.pack(pady=(0, 20))

# URL Section
url_frame = tk.LabelFrame(main_frame, text="🔗 رابط التحميل", font=label_font,
                         bg=COLORS['white'], fg=COLORS['primary'], padx=15, pady=10)
url_frame.pack(fill=tk.X, pady=(0, 15))

url_entry = tk.Entry(url_frame, font=label_font, width=60, relief=tk.FLAT,
                    bd=5, bg=COLORS['white'], fg=COLORS['primary'])
url_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

# URL buttons frame
url_buttons_frame = tk.Frame(url_frame, bg=COLORS['white'])
url_buttons_frame.pack(side=tk.RIGHT)

paste_btn = tk.Button(url_buttons_frame, text="📋 لصق", command=paste_from_clipboard,
                     font=button_font, bg=COLORS['secondary'], fg="white", relief=tk.FLAT,
                     padx=10, pady=5, cursor="hand2")
paste_btn.pack(side=tk.LEFT, padx=(0, 5))

clear_btn = tk.Button(url_buttons_frame, text="🗑️ مسح", command=clear_url,
                     font=button_font, bg=COLORS['danger'], fg="white", relief=tk.FLAT,
                     padx=10, pady=5, cursor="hand2")
clear_btn.pack(side=tk.LEFT)

# Auto-paste from clipboard
try:
    clipboard_url = pyperclip.paste()
    if isinstance(clipboard_url, str) and clipboard_url.startswith(('http://', 'https://')):
        url_entry.insert(0, clipboard_url)
except:
    pass

# Folder Section
folder_frame = tk.LabelFrame(main_frame, text="📂 مجلد الحفظ", font=label_font,
                           bg=COLORS['white'], fg=COLORS['primary'], padx=15, pady=10)
folder_frame.pack(fill=tk.X, pady=(0, 15))

folder_entry = tk.Entry(folder_frame, font=label_font, width=50, relief=tk.FLAT,
                       bd=5, bg=COLORS['white'], fg=COLORS['primary'])
folder_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

browse_btn = tk.Button(folder_frame, text="📁 استعراض", command=browse_folder,
                      font=button_font, bg=COLORS['purple'], fg="white", relief=tk.FLAT,
                      padx=15, pady=5, cursor="hand2")
browse_btn.pack(side=tk.RIGHT)

# Options Section
options_frame = tk.LabelFrame(main_frame, text="⚙️ خيارات التحميل", font=label_font,
                            bg=COLORS['white'], fg=COLORS['primary'], padx=15, pady=10)
options_frame.pack(fill=tk.X, pady=(0, 15))

audio_var = tk.BooleanVar()
audio_check = tk.Checkbutton(options_frame, text="🎵 تحميل الصوت فقط (YouTube)",
                           variable=audio_var, font=label_font, bg=COLORS['white'],
                           fg=COLORS['primary'], selectcolor=COLORS['white'], cursor="hand2")
audio_check.pack(anchor=tk.W)

# Progress Section
progress_frame = tk.LabelFrame(main_frame, text="📊 حالة التحميل", font=label_font,
                             bg=COLORS['white'], fg=COLORS['primary'], padx=15, pady=10)
progress_frame.pack(fill=tk.X, pady=(0, 15))

status_label = tk.Label(progress_frame, text=MESSAGES['ready'],
                       font=label_font, bg=COLORS['white'], fg=COLORS['dark'])
status_label.pack(anchor=tk.W, pady=(0, 10))

progress_bar = ttk.Progressbar(progress_frame, length=400, mode='determinate')
progress_bar.pack(fill=tk.X)

# Download Button
download_btn = tk.Button(main_frame, text="⬇️ ابدأ التحميل", command=start_download,
                        font=button_font, bg=COLORS['success'], fg="white", relief=tk.FLAT,
                        padx=30, pady=15, cursor="hand2")
download_btn.pack(pady=20)

# Footer
footer_label = tk.Label(main_frame, text="💡 يدعم تحميل الملفات العادية وفيديوهات YouTube",
                       font=("Arial", 9), bg=COLORS['light'], fg=COLORS['gray'])
footer_label.pack(pady=(10, 0))

root.mainloop()
